import axios, { AxiosError } from "axios";
import {
  BroadcastRequest,
  BroadcastResponse,
  ImageUploadResponse,
  ApiError,
  API_CONFIG,
} from "./types";

// Configuração do cliente Axios
const apiClient = axios.create({
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    "Content-Type": "application/json",
  },
});

// Interceptor para tratamento de erros
apiClient.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    const apiError: ApiError = {
      message: "Erro na comunicação com o servidor",
      status: error.response?.status,
      code: error.code,
      details: error.response?.data,
    };

    // Tratamento específico baseado no status HTTP
    if (error.response?.status === 400) {
      apiError.message = "Dados inválidos enviados para o servidor";

      // Verificar se há detalhes específicos do erro
      if (error.response.data && typeof error.response.data === "object") {
        const errorData = error.response.data as any;
        if (errorData.message) {
          apiError.message = errorData.message;
        } else if (errorData.error) {
          apiError.message = errorData.error;
        }
      }
    } else if (error.response?.status === 401) {
      apiError.message = "Credenciais de API inválidas ou expiradas";
    } else if (error.response?.status === 403) {
      apiError.message = "Acesso negado. Verifique as permissões da API";
    } else if (error.response?.status === 404) {
      apiError.message = "Endpoint da API não encontrado";
    } else if (error.response?.status === 422) {
      apiError.message = "Dados de entrada inválidos";

      // Tentar extrair detalhes de validação
      if (error.response.data && typeof error.response.data === "object") {
        const errorData = error.response.data as any;
        if (errorData.errors && Array.isArray(errorData.errors)) {
          apiError.message = `Erros de validação: ${errorData.errors.join(
            ", "
          )}`;
        }
      }
    } else if (error.response?.status === 429) {
      apiError.message =
        "Muitas requisições. Tente novamente em alguns minutos";
    } else if (error.response?.status >= 500) {
      apiError.message = "Erro interno do servidor. Tente novamente mais tarde";
    } else if (error.code === "ECONNABORTED") {
      apiError.message = "Timeout na requisição. Verifique sua conexão";
    } else if (error.code === "ERR_NETWORK") {
      apiError.message = "Erro de rede. Verifique sua conexão com a internet";
    } else if (error.code === "ERR_CANCELED") {
      apiError.message = "Requisição cancelada";
    }

    console.error("Erro da API:", {
      status: error.response?.status,
      code: error.code,
      message: apiError.message,
      details: error.response?.data,
    });

    return Promise.reject(apiError);
  }
);

/**
 * Valida a resposta da API de broadcast
 */
const validateBroadcastResponse = (data: any): BroadcastResponse => {
  // Se a resposta não tem a estrutura esperada, criar uma resposta padrão
  if (!data || typeof data !== "object") {
    return {
      success: false,
      message: "Resposta inválida da API",
    };
  }

  // Se a API retornou sucesso explicitamente
  if (data.success === true || data.status === "success") {
    return {
      success: true,
      message: data.message || "Mensagens enviadas com sucesso",
      data: {
        messageId: data.messageId || data.id,
        sentCount:
          data.sentCount ||
          data.sent ||
          (data.results ? data.results.length : undefined),
        failedCount: data.failedCount || data.failed || 0,
        details: data.details || data.results,
      },
    };
  }

  // Se a API retornou erro explicitamente
  if (data.success === false || data.status === "error") {
    return {
      success: false,
      message: data.message || data.error || "Erro no envio das mensagens",
      data: data.data,
    };
  }

  // Para APIs que não retornam status explícito, assumir sucesso se não há erro
  return {
    success: true,
    message: "Mensagens processadas",
    data: {
      sentCount: Array.isArray(data) ? data.length : 1,
      failedCount: 0,
    },
  };
};

/**
 * Envia mensagem de broadcast para múltiplos números
 */
export const sendBroadcastMessage = async (
  phones: string[],
  message: string,
  imageUrl?: string
): Promise<BroadcastResponse> => {
  try {
    // Validar parâmetros de entrada
    if (!phones || phones.length === 0) {
      throw new Error("Lista de números de telefone é obrigatória");
    }

    if (!message || message.trim().length === 0) {
      throw new Error("Mensagem é obrigatória");
    }

    // Preparar dados da requisição
    const requestData: BroadcastRequest = {
      number: phones,
      apiKey: API_CONFIG.API_KEY,
      intanceName: API_CONFIG.INSTANCE_NAME,
      caption: message.trim(),
      type: imageUrl ? "media" : "text",
    };

    // Adicionar URL da imagem se fornecida
    if (imageUrl) {
      requestData.media = imageUrl;
    }

    console.log("Enviando requisição de broadcast:", {
      ...requestData,
      apiKey: "***HIDDEN***", // Não logar a API key
    });

    const response = await apiClient.post<any>(
      API_CONFIG.BROADCAST_URL,
      requestData
    );

    console.log("Resposta da API:", response.data);

    // Validar e normalizar a resposta
    const validatedResponse = validateBroadcastResponse(response.data);

    return validatedResponse;
  } catch (error) {
    console.error("Erro ao enviar broadcast:", error);

    // Se é um erro da nossa validação ou da API, repassar
    if (
      error instanceof Error ||
      (error && typeof error === "object" && "message" in error)
    ) {
      throw error;
    }

    // Para outros tipos de erro, criar um erro genérico
    throw new Error("Falha inesperada no envio das mensagens");
  }
};

/**
 * Faz upload de imagem e retorna URL pública
 * Nota: Esta é uma implementação placeholder.
 * Você precisará implementar o upload real baseado no seu provedor de storage.
 */
export const uploadImage = async (file: File): Promise<string> => {
  try {
    // TODO: Implementar upload real para seu provedor de storage
    // Por enquanto, vamos simular um upload e retornar uma URL de exemplo

    console.log("Simulando upload de imagem:", file.name);

    // Simular delay de upload
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Retornar URL simulada
    // Em produção, substitua por upload real para AWS S3, Cloudinary, etc.
    const simulatedUrl = `https://exemplo.com/uploads/${Date.now()}-${
      file.name
    }`;

    console.log("Upload simulado concluído:", simulatedUrl);

    return simulatedUrl;
  } catch (error) {
    console.error("Erro no upload da imagem:", error);
    throw new Error("Falha no upload da imagem. Tente novamente.");
  }
};

/**
 * Valida formato de número de telefone para a API
 */
export const validatePhoneForApi = (phone: string): boolean => {
  // Remove espaços e caracteres especiais, mantendo apenas números e +
  const cleanPhone = phone.replace(/[^\d+]/g, "");

  // Verifica se está no formato esperado pela API (apenas números, sem +)
  // Exemplo: 5535999545089
  const phoneRegex = /^\d{10,15}$/;

  return phoneRegex.test(cleanPhone.replace("+", ""));
};

/**
 * Formata número de telefone para o formato esperado pela API
 */
export const formatPhoneForApi = (phone: string): string => {
  // Remove todos os caracteres não numéricos exceto +
  let cleanPhone = phone.replace(/[^\d+]/g, "");

  // Remove o + se presente
  if (cleanPhone.startsWith("+")) {
    cleanPhone = cleanPhone.substring(1);
  }

  return cleanPhone;
};
