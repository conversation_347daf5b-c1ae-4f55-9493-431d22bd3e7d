// Tipos para a API de broadcast de mensagens

export interface BroadcastRequest {
  number: string[];
  media?: string;
  apiKey: string;
  intanceName: string;
  caption: string;
  type: "text" | "media";
}

export interface BroadcastResponse {
  success: boolean;
  message: string;
  data?: {
    messageId?: string;
    sentCount?: number;
    failedCount?: number;
    details?: Array<{
      number: string;
      status: "sent" | "failed";
      error?: string;
    }>;
  };
}

export interface ImageUploadRequest {
  file: File;
}

export interface ImageUploadResponse {
  success: boolean;
  url?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

// Tipos para o formulário local
export interface MessageFormData {
  phones: string[];
  message: string;
  image: File | null;
}

// Configurações da API
export const API_CONFIG = {
  BROADCAST_URL:
    import.meta.env.VITE_BROADCAST_URL ||
    "https://n8n.nerve.com.br/webhook-test/broadcast",
  API_KEY:
    import.meta.env.VITE_API_KEY || "DC37217D58F3-4319-849B-EB53B8A8E274",
  INSTANCE_NAME: import.meta.env.VITE_INSTANCE_NAME || "001",
  TIMEOUT: 30000, // 30 segundos
} as const;
