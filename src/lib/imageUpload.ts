import { ImageUploadResponse } from './types';

/**
 * Configurações para upload de imagem
 */
const UPLOAD_CONFIG = {
  // Para implementação futura com provedor real
  CLOUDINARY_UPLOAD_URL: 'https://api.cloudinary.com/v1_1/YOUR_CLOUD_NAME/image/upload',
  CLOUDINARY_UPLOAD_PRESET: 'YOUR_UPLOAD_PRESET',
  
  // Configurações de validação
  MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  
  // Timeout para upload
  TIMEOUT: 30000, // 30 segundos
} as const;

/**
 * Valida arquivo de imagem antes do upload
 */
export const validateImageFile = (file: File): string | null => {
  if (!UPLOAD_CONFIG.ALLOWED_TYPES.includes(file.type)) {
    return 'Apenas arquivos JPG, PNG e WEBP são permitidos';
  }
  
  if (file.size > UPLOAD_CONFIG.MAX_FILE_SIZE) {
    return 'O arquivo deve ter no máximo 2MB';
  }
  
  return null;
};

/**
 * Upload de imagem para Cloudinary (implementação de exemplo)
 * Substitua por seu provedor preferido
 */
export const uploadToCloudinary = async (file: File): Promise<string> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('upload_preset', UPLOAD_CONFIG.CLOUDINARY_UPLOAD_PRESET);
  
  try {
    const response = await fetch(UPLOAD_CONFIG.CLOUDINARY_UPLOAD_URL, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.secure_url;
  } catch (error) {
    console.error('Erro no upload para Cloudinary:', error);
    throw new Error('Falha no upload da imagem');
  }
};

/**
 * Upload de imagem para AWS S3 (implementação de exemplo)
 * Requer configuração de presigned URLs no backend
 */
export const uploadToS3 = async (file: File): Promise<string> => {
  try {
    // 1. Obter presigned URL do seu backend
    const presignedResponse = await fetch('/api/upload/presigned-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileName: file.name,
        fileType: file.type,
      }),
    });
    
    if (!presignedResponse.ok) {
      throw new Error('Falha ao obter URL de upload');
    }
    
    const { uploadUrl, fileUrl } = await presignedResponse.json();
    
    // 2. Upload direto para S3
    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });
    
    if (!uploadResponse.ok) {
      throw new Error('Falha no upload para S3');
    }
    
    return fileUrl;
  } catch (error) {
    console.error('Erro no upload para S3:', error);
    throw new Error('Falha no upload da imagem');
  }
};

/**
 * Simulação de upload para desenvolvimento/teste
 * Remove esta função em produção
 */
export const simulateImageUpload = async (file: File): Promise<string> => {
  console.log('Simulando upload de imagem:', file.name);
  
  // Simular delay de upload
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Simular possível falha (5% de chance)
  if (Math.random() < 0.05) {
    throw new Error('Falha simulada no upload');
  }
  
  // Retornar URL simulada
  const timestamp = Date.now();
  const simulatedUrl = `https://exemplo.com/uploads/${timestamp}-${file.name}`;
  
  console.log('Upload simulado concluído:', simulatedUrl);
  return simulatedUrl;
};

/**
 * Função principal de upload de imagem
 * Escolhe o provedor baseado na configuração
 */
export const uploadImage = async (file: File): Promise<string> => {
  // Validar arquivo antes do upload
  const validationError = validateImageFile(file);
  if (validationError) {
    throw new Error(validationError);
  }
  
  try {
    // Em desenvolvimento, usar simulação
    if (process.env.NODE_ENV === 'development') {
      return await simulateImageUpload(file);
    }
    
    // Em produção, escolher provedor real
    // Descomente a linha apropriada baseada no seu provedor:
    
    // return await uploadToCloudinary(file);
    // return await uploadToS3(file);
    
    // Por enquanto, usar simulação mesmo em produção
    return await simulateImageUpload(file);
    
  } catch (error) {
    console.error('Erro no upload da imagem:', error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error('Falha no upload da imagem. Tente novamente.');
  }
};

/**
 * Cria preview local da imagem
 */
export const createImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Falha ao criar preview da imagem'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Falha ao ler arquivo de imagem'));
    };
    
    reader.readAsDataURL(file);
  });
};
