# Integração com API de Broadcast

Este documento descreve a implementação da integração real com a API de envio de mensagens no projeto Message Blast Ignition.

## Visão Geral

A integração substitui a simulação anterior por chamadas reais à API de broadcast, mantendo toda a UX existente e adicionando validações robustas.

## Configuração da API

### Endpoint
- **URL**: `https://n8n.nerve.com.br/webhook-test/broadcast`
- **Método**: POST
- **Content-Type**: application/json

### Credenciais
As credenciais estão configuradas em `src/lib/types.ts`:
```typescript
export const API_CONFIG = {
  BROADCAST_URL: 'https://n8n.nerve.com.br/webhook-test/broadcast',
  API_KEY: 'DC37217D58F3-4319-849B-EB53B8A8E274',
  INSTANCE_NAME: '001',
  TIMEOUT: 30000, // 30 segundos
} as const;
```

## Estrutura da Requisição

### Para mensagens apenas de texto:
```json
{
  "number": ["5535999545089", "5535998991114"],
  "apiKey": "DC37217D58F3-4319-849B-EB53B8A8E274",
  "intanceName": "001",
  "caption": "Texto da mensagem",
  "type": "text"
}
```

### Para mensagens com imagem:
```json
{
  "number": ["5535999545089", "5535998991114"],
  "media": "https://exemplo.com/imagem.jpg",
  "apiKey": "DC37217D58F3-4319-849B-EB53B8A8E274",
  "intanceName": "001",
  "caption": "Texto da mensagem",
  "type": "media"
}
```

## Fluxo de Funcionamento

### 1. Validação de Números
- Números são validados usando regex: `/^\d{10,15}$/`
- Formatação automática remove caracteres especiais
- Números inválidos são reportados ao usuário

### 2. Upload de Imagem (se presente)
- Validação de tipo (JPG, PNG, WEBP) e tamanho (máx 2MB)
- Upload simulado retorna URL pública
- **Nota**: Implementação real de upload deve ser configurada

### 3. Envio da Mensagem
- Requisição POST para a API
- Validação da resposta
- Feedback detalhado ao usuário

## Tratamento de Erros

### Tipos de Erro Tratados
- **400**: Dados inválidos
- **401**: Credenciais inválidas
- **403**: Acesso negado
- **404**: Endpoint não encontrado
- **422**: Dados de entrada inválidos
- **429**: Muitas requisições
- **500+**: Erro interno do servidor
- **Timeout**: Problema de conexão
- **Network**: Erro de rede

### Feedback ao Usuário
- Mensagens de erro específicas e claras
- Sugestões de ação quando apropriado
- Logs detalhados no console para debug

## Arquivos Modificados

### Novos Arquivos
- `src/lib/types.ts` - Tipos TypeScript e configurações
- `src/lib/api.ts` - Serviço de API com Axios
- `src/lib/imageUpload.ts` - Serviço de upload de imagem

### Arquivos Modificados
- `src/components/MessageForm.tsx` - Integração com API real

## Configuração de Upload de Imagem

### Implementação Atual (Simulação)
```typescript
// Em desenvolvimento, usar simulação
if (process.env.NODE_ENV === 'development') {
  return await simulateImageUpload(file);
}
```

### Para Produção
Descomente e configure um dos provedores em `src/lib/imageUpload.ts`:

#### Cloudinary
```typescript
return await uploadToCloudinary(file);
```

#### AWS S3
```typescript
return await uploadToS3(file);
```

## Testes Recomendados

### Cenários de Teste
1. **Apenas texto**: Envio para um número
2. **Apenas texto**: Envio para múltiplos números
3. **Texto + imagem**: Upload e envio
4. **Números inválidos**: Validação e feedback
5. **Erro de rede**: Tratamento de falhas
6. **Timeout**: Comportamento em conexões lentas

### Como Testar
1. Execute `npm run dev`
2. Acesse `http://localhost:8080`
3. Teste os diferentes cenários
4. Verifique logs no console do navegador

## Monitoramento

### Logs Disponíveis
- Requisições enviadas (API key ocultada)
- Respostas da API
- Erros detalhados
- Tempo de upload de imagens

### Console do Navegador
Abra as ferramentas de desenvolvedor para ver logs detalhados:
```javascript
// Exemplo de log de requisição
console.log("Enviando requisição de broadcast:", {
  number: ["5535999545089"],
  apiKey: "***HIDDEN***",
  // ...
});
```

## Próximos Passos

1. **Configurar upload real de imagem**
2. **Implementar retry automático para falhas temporárias**
3. **Adicionar cache de respostas**
4. **Implementar rate limiting local**
5. **Adicionar métricas de performance**

## Suporte

Para problemas ou dúvidas sobre a integração:
1. Verifique os logs no console
2. Teste com números válidos
3. Confirme conectividade com a API
4. Verifique se as credenciais estão corretas
