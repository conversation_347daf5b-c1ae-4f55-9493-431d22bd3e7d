import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { MessageCircle, Users, Image as ImageIcon } from "lucide-react";

interface MessagePreviewProps {
  phones: string[];
  message: string;
  image: File | null;
  imagePreview?: string | null;
  title?: string;
}

const MessagePreview: React.FC<MessagePreviewProps> = ({
  phones,
  message,
  image,
  imagePreview,
  title,
}) => {
  if (phones.length === 0 && !message.trim() && !image && !title?.trim()) {
    return null;
  }

  return (
    <Card className="border-launcher-secondary/30">
      <CardHeader className="bg-launcher-primary/5">
        <CardTitle className="flex items-center gap-2 text-launcher-primary">
          <MessageCircle className="h-5 w-5" />
          Pré-visualização da Mensagem
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 pt-6">
        {/* Destinatários */}
        {phones.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-4 w-4 text-launcher-secondary" />
              <span className="font-medium text-launcher-primary">
                Será enviado para {phones.length} número
                {phones.length > 1 ? "s" : ""}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              {phones.slice(0, 3).join(", ")}
              {phones.length > 3 && ` e mais ${phones.length - 3} números...`}
            </div>
          </div>
        )}

        {/* Preview da mensagem */}
        <div className="border border-launcher-secondary/20 rounded-lg p-4 bg-white shadow-sm">
          {title?.trim() && (
            <div className="mb-3 pb-3 border-b border-gray-200">
              <h3 className="font-semibold text-launcher-primary text-lg">
                {title}
              </h3>
            </div>
          )}

          {imagePreview && (
            <div className="mb-3">
              <img
                src={imagePreview}
                alt="Preview da imagem"
                className="max-w-full h-auto max-h-48 rounded-md border"
              />
            </div>
          )}

          {image && !imagePreview && (
            <div className="mb-3 flex items-center gap-2 p-2 bg-gray-50 rounded border">
              <ImageIcon className="h-4 w-4 text-launcher-secondary" />
              <span className="text-sm text-launcher-primary">
                {image.name}
              </span>
            </div>
          )}

          {message.trim() && (
            <div className="text-launcher-primary whitespace-pre-wrap">
              {message}
            </div>
          )}

          {!displayMessage.trim() && !image && !title?.trim() && (
            <div className="text-gray-400 italic text-center py-4">
              Sua mensagem aparecerá aqui...
            </div>
          )}
        </div>

        {/* Informações adicionais */}
        <div className="text-sm text-gray-600 space-y-1">
          {displayMessage.trim() && (
            <div className="flex justify-between">
              <span>Caracteres: {displayMessage.length}/1000</span>
              {useAiEnhancement && enhancedMessage && (
                <span className="text-purple-600">
                  Original: {message.length} → Melhorada:{" "}
                  {enhancedMessage.length}
                </span>
              )}
            </div>
          )}
          {image && (
            <div>Imagem: {(image.size / 1024 / 1024).toFixed(2)} MB</div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MessagePreview;
