import React, { useState, useRef } from "react";
import { X, Plus, Phone, Upload, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";

interface PhoneInputListProps {
  phones: string[];
  onChange: (phones: string[]) => void;
  error?: string;
}

const PhoneInputList: React.FC<PhoneInputListProps> = ({
  phones,
  onChange,
  error,
}) => {
  const [newPhone, setNewPhone] = useState("");
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [columnName, setColumnName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/\s+/g, ""));
  };

  const addPhone = () => {
    const cleanPhone = newPhone.trim();
    if (
      cleanPhone &&
      validatePhone(cleanPhone) &&
      !phones.includes(cleanPhone)
    ) {
      onChange([...phones, cleanPhone]);
      setNewPhone("");
    }
  };

  const removePhone = (index: number) => {
    const updatedPhones = phones.filter((_, i) => i !== index);
    onChange(updatedPhones);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addPhone();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    const phoneNumbers = pastedText
      .split(/[\n,;]/)
      .map((phone) => phone.trim())
      .filter(
        (phone) => phone && validatePhone(phone) && !phones.includes(phone)
      );

    if (phoneNumbers.length > 0) {
      onChange([...phones, ...phoneNumbers]);
    }
  };

  // Função para processar arquivo CSV
  const parseCSV = (content: string, columnName: string): string[] => {
    const lines = content.split("\n").filter((line) => line.trim());
    if (lines.length === 0) return [];

    const headers = lines[0]
      .split(",")
      .map((h) => h.trim().toLowerCase().replace(/"/g, ""));
    const columnIndex = headers.findIndex(
      (h) => h === columnName.toLowerCase()
    );

    if (columnIndex === -1) {
      throw new Error(
        `Coluna "${columnName}" não encontrada. Colunas disponíveis: ${headers.join(
          ", "
        )}`
      );
    }

    const phoneNumbers: string[] = [];
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(",");
      if (values[columnIndex]) {
        const phone = values[columnIndex].trim().replace(/"/g, "");
        if (phone) phoneNumbers.push(phone);
      }
    }

    return phoneNumbers;
  };

  // Função para processar arquivo XML
  const parseXML = (content: string, columnName: string): string[] => {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(content, "text/xml");

    if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
      throw new Error("Arquivo XML inválido");
    }

    const phoneNumbers: string[] = [];
    const elements = xmlDoc.getElementsByTagName(columnName.toLowerCase());

    if (elements.length === 0) {
      // Tentar buscar por atributos ou outras variações
      const allElements = xmlDoc.getElementsByTagName("*");
      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];
        if (element.tagName.toLowerCase() === columnName.toLowerCase()) {
          const phone = element.textContent?.trim();
          if (phone) phoneNumbers.push(phone);
        }
      }

      if (phoneNumbers.length === 0) {
        throw new Error(`Elemento "${columnName}" não encontrado no XML`);
      }
    } else {
      for (let i = 0; i < elements.length; i++) {
        const phone = elements[i].textContent?.trim();
        if (phone) phoneNumbers.push(phone);
      }
    }

    return phoneNumbers;
  };

  // Função para abrir seletor de arquivo
  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  // Função para lidar com seleção de arquivo
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const fileExtension = file.name.split(".").pop()?.toLowerCase();
      if (fileExtension === "csv" || fileExtension === "xml") {
        setSelectedFile(file);
        setColumnName("");
        setIsImportDialogOpen(true);
      } else {
        toast.error("Formato de arquivo não suportado", {
          description: "Apenas arquivos .csv e .xml são aceitos.",
        });
      }
    }
    // Limpar o input para permitir selecionar o mesmo arquivo novamente
    event.target.value = "";
  };

  // Função para processar a importação
  const handleImport = async () => {
    if (!selectedFile || !columnName.trim()) {
      toast.error("Dados incompletos", {
        description: "Selecione um arquivo e especifique o nome da coluna.",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const fileContent = await selectedFile.text();
      const fileExtension = selectedFile.name.split(".").pop()?.toLowerCase();

      let extractedPhones: string[] = [];

      if (fileExtension === "csv") {
        extractedPhones = parseCSV(fileContent, columnName.trim());
      } else if (fileExtension === "xml") {
        extractedPhones = parseXML(fileContent, columnName.trim());
      }

      if (extractedPhones.length === 0) {
        toast.error("Nenhum telefone encontrado", {
          description: `Não foram encontrados dados na coluna "${columnName}".`,
        });
        return;
      }

      // Validar e filtrar telefones
      const validPhones: string[] = [];
      const invalidCount = extractedPhones.length;

      extractedPhones.forEach((phone) => {
        const cleanPhone = phone.trim();
        if (
          cleanPhone &&
          validatePhone(cleanPhone) &&
          !phones.includes(cleanPhone)
        ) {
          validPhones.push(cleanPhone);
        }
      });

      const finalInvalidCount = invalidCount - validPhones.length;

      if (validPhones.length > 0) {
        onChange([...phones, ...validPhones]);
        toast.success("Telefones importados com sucesso!", {
          description: `${validPhones.length} telefones adicionados${
            finalInvalidCount > 0
              ? `, ${finalInvalidCount} inválidos ignorados`
              : ""
          }.`,
        });
      } else {
        toast.error("Nenhum telefone válido encontrado", {
          description:
            "Todos os telefones no arquivo são inválidos ou já estão na lista.",
        });
      }

      // Fechar modal
      setIsImportDialogOpen(false);
      setSelectedFile(null);
      setColumnName("");
    } catch (error) {
      console.error("Erro ao processar arquivo:", error);
      toast.error("Erro ao processar arquivo", {
        description:
          error instanceof Error ? error.message : "Erro desconhecido.",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label
          htmlFor="phone-input"
          className="text-launcher-primary font-medium"
        >
          Números de Telefone *
        </Label>
        <div className="flex gap-2 mt-2">
          <div className="relative flex-1">
            <Phone className="absolute left-3 top-3 h-4 w-4 text-launcher-secondary" />
            <Input
              id="phone-input"
              type="tel"
              placeholder="+55 11 99999-9999"
              value={newPhone}
              onChange={(e) => setNewPhone(e.target.value)}
              onKeyPress={handleKeyPress}
              onPaste={handlePaste}
              className="pl-10 border-launcher-secondary/30 focus:border-launcher-accent"
            />
          </div>
          <Button
            type="button"
            onClick={addPhone}
            disabled={!newPhone.trim() || !validatePhone(newPhone.trim())}
            className="bg-launcher-secondary hover:bg-launcher-accent text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            onClick={handleImportClick}
            variant="outline"
            className="border-launcher-secondary/30 text-launcher-secondary hover:bg-launcher-secondary hover:text-white"
            title="Importar Telefones"
          >
            <Upload className="h-4 w-4" />
          </Button>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv,.xml"
          onChange={handleFileSelect}
          className="hidden"
        />
        <p className="text-sm text-gray-600 mt-1">
          Cole múltiplos números separados por vírgula, quebra de linha ou
          importe de arquivo CSV/XML
        </p>
      </div>

      {phones.length > 0 && (
        <div className="space-y-2">
          <Label className="text-launcher-primary font-medium">
            Números Adicionados ({phones.length})
          </Label>
          <div className="max-h-32 overflow-y-auto space-y-1 p-3 bg-gray-50 rounded-md border">
            {phones.map((phone, index) => (
              <div
                key={index}
                className="flex items-center justify-between bg-white p-2 rounded border"
              >
                <span className="text-launcher-primary font-mono text-sm">
                  {phone}
                </span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removePhone(index)}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {error && <p className="text-red-500 text-sm font-medium">{error}</p>}

      {/* Modal de Configuração de Importação */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Importar Telefones</DialogTitle>
            <DialogDescription>
              Configure a importação do arquivo {selectedFile?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="column-name">
                Nome da coluna que contém os telefones
              </Label>
              <Input
                id="column-name"
                placeholder="Ex: telefone, phone, numero, celular"
                value={columnName}
                onChange={(e) => setColumnName(e.target.value)}
                disabled={isProcessing}
              />
              <p className="text-sm text-gray-600">
                Digite o nome da coluna (não diferencia maiúsculas/minúsculas)
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsImportDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleImport}
              disabled={!columnName.trim() || isProcessing}
              className="bg-launcher-secondary hover:bg-launcher-accent text-white"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processando...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Importar
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PhoneInputList;
