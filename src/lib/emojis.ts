export interface EmojiData {
  emoji: string;
  tags: string[];
}

export interface EmojiCategory {
  name: string;
  icon: string;
  emojis: EmojiData[];
}

// Função para processar o arquivo emojis.txt
export const parseEmojisFile = (content: string): EmojiData[] => {
  const lines = content.split('\n').filter(line => line.trim());
  const emojis: EmojiData[] = [];

  lines.forEach(line => {
    const trimmedLine = line.trim();
    if (!trimmedLine) return;

    // Extrair emoji (primeiro caractere/sequência Unicode)
    const emojiMatch = trimmedLine.match(/^(\S+)/);
    if (!emojiMatch) return;

    const emoji = emojiMatch[1];
    
    // Extrair tags (tudo após o emoji)
    const tagsString = trimmedLine.substring(emoji.length).trim();
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);

    if (emoji && tags.length > 0) {
      emojis.push({ emoji, tags });
    }
  });

  return emojis;
};

// Função para categorizar emojis baseado nas tags
export const categorizeEmojis = (emojis: EmojiData[]): EmojiCategory[] => {
  const categories: { [key: string]: EmojiData[] } = {
    faces: [],
    people: [],
    animals: [],
    food: [],
    travel: [],
    activities: [],
    objects: [],
    symbols: [],
    flags: [],
    nature: [],
  };

  emojis.forEach(emojiData => {
    const { tags } = emojiData;
    let categorized = false;

    // Categorizar baseado nas tags
    if (tags.some(tag => 
      ['face', 'smile', 'happy', 'sad', 'angry', 'laugh', 'cry', 'wink', 'kiss'].includes(tag.toLowerCase())
    )) {
      categories.faces.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['man', 'woman', 'person', 'people', 'family', 'baby', 'child', 'boy', 'girl'].includes(tag.toLowerCase())
    )) {
      categories.people.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['animal', 'cat', 'dog', 'bird', 'fish', 'bear', 'lion', 'tiger', 'monkey', 'elephant'].includes(tag.toLowerCase())
    )) {
      categories.animals.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['food', 'fruit', 'restaurant', 'drink', 'sweet', 'cooking', 'eat', 'hungry'].includes(tag.toLowerCase())
    )) {
      categories.food.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['travel', 'car', 'plane', 'train', 'boat', 'vehicle', 'transport'].includes(tag.toLowerCase())
    )) {
      categories.travel.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['game', 'sport', 'party', 'music', 'entertainment', 'celebration'].includes(tag.toLowerCase())
    )) {
      categories.activities.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['tool', 'computer', 'phone', 'book', 'clothes', 'clothing', 'object'].includes(tag.toLowerCase())
    )) {
      categories.objects.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['flag'].includes(tag.toLowerCase())
    )) {
      categories.flags.push(emojiData);
      categorized = true;
    }
    
    if (tags.some(tag => 
      ['nature', 'plant', 'flower', 'tree', 'weather', 'sun', 'moon', 'star'].includes(tag.toLowerCase())
    )) {
      categories.nature.push(emojiData);
      categorized = true;
    }

    // Se não foi categorizado, adicionar em símbolos
    if (!categorized) {
      categories.symbols.push(emojiData);
    }
  });

  return [
    { name: 'Rostos', icon: '😀', emojis: categories.faces },
    { name: 'Pessoas', icon: '👥', emojis: categories.people },
    { name: 'Animais', icon: '🐶', emojis: categories.animals },
    { name: 'Comida', icon: '🍎', emojis: categories.food },
    { name: 'Viagem', icon: '✈️', emojis: categories.travel },
    { name: 'Atividades', icon: '⚽', emojis: categories.activities },
    { name: 'Objetos', icon: '💡', emojis: categories.objects },
    { name: 'Natureza', icon: '🌿', emojis: categories.nature },
    { name: 'Bandeiras', icon: '🏁', emojis: categories.flags },
    { name: 'Símbolos', icon: '❤️', emojis: categories.symbols },
  ].filter(category => category.emojis.length > 0);
};

// Função para carregar e processar emojis
export const loadEmojis = async (): Promise<EmojiCategory[]> => {
  try {
    const response = await fetch('/emojis.txt');
    const content = await response.text();
    const emojis = parseEmojisFile(content);
    return categorizeEmojis(emojis);
  } catch (error) {
    console.error('Erro ao carregar emojis:', error);
    return [];
  }
};

// Função para buscar emojis
export const searchEmojis = (emojis: EmojiData[], query: string): EmojiData[] => {
  if (!query.trim()) return emojis;
  
  const searchTerm = query.toLowerCase();
  return emojis.filter(emojiData => 
    emojiData.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
};
