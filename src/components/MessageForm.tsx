import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Send, Loader2, Eye, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import PhoneInputList from "./PhoneInputList";
import ImageUploader from "./ImageUploader";
import MessagePreview from "./MessagePreview";
import {
  sendBroadcastMessage,
  formatPhoneForApi,
  validatePhoneForApi,
} from "@/lib/api";
import { uploadImage } from "@/lib/imageUpload";
import { ApiError } from "@/lib/types";

const messageSchema = z.object({
  phones: z
    .array(z.string())
    .min(1, "Adicione pelo menos um número de telefone"),
  message: z
    .string()
    .min(1, "A mensagem é obrigatória")
    .max(1000, "Mensagem muito longa (máximo 1000 caracteres)"),
  image: z.instanceof(File).optional().nullable(),
  title: z
    .string()
    .min(1, "O título é obrigatório")
    .max(100, "Título muito longo (máximo 100 caracteres)"),
  apiKey: z.string().min(1, "A API Key é obrigatória"),
  instanceName: z.string().min(1, "O nome da instância é obrigatório"),
});

type MessageFormData = z.infer<typeof messageSchema>;

const MessageForm: React.FC = () => {
  const [phones, setPhones] = useState<string[]>([]);
  const [message, setMessage] = useState("");
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [title, setTitle] = useState("");
  const [apiKey, setApiKey] = useState(import.meta.env.VITE_API_KEY || "");
  const [instanceName, setInstanceName] = useState(
    import.meta.env.VITE_INSTANCE_NAME || ""
  );
  const [showApiKey, setShowApiKey] = useState(false);
  const [useAiEnhancement, setUseAiEnhancement] = useState(false);

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    trigger,
    reset,
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      phones: [],
      message: "",
      image: null,
      title: "",
      apiKey: import.meta.env.VITE_API_KEY || "",
      instanceName: import.meta.env.VITE_INSTANCE_NAME || "",
    },
  });

  // Update form values when state changes
  useEffect(() => {
    setValue("phones", phones);
    setValue("message", message);
    setValue("image", image);
    setValue("title", title);
    setValue("apiKey", apiKey);
    setValue("instanceName", instanceName);
  }, [phones, message, image, title, apiKey, instanceName, setValue]);

  // Create image preview
  useEffect(() => {
    if (image) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(image);
    } else {
      setImagePreview(null);
    }
  }, [image]);

  const onSubmit = async (data: MessageFormData) => {
    setIsSubmitting(true);

    try {
      // Validar e formatar números de telefone
      const validPhones: string[] = [];
      const invalidPhones: string[] = [];

      data.phones.forEach((phone) => {
        if (validatePhoneForApi(phone)) {
          validPhones.push(formatPhoneForApi(phone));
        } else {
          invalidPhones.push(phone);
        }
      });

      // Verificar se há números inválidos
      if (invalidPhones.length > 0) {
        toast.error("Números de telefone inválidos encontrados", {
          description: `Verifique: ${invalidPhones.join(", ")}`,
        });
        return;
      }

      if (validPhones.length === 0) {
        toast.error("Nenhum número válido encontrado");
        return;
      }

      let imageUrl: string | undefined;

      // Upload da imagem se fornecida
      if (data.image) {
        try {
          toast.info("Fazendo upload da imagem...", {
            description: "Aguarde enquanto a imagem é processada.",
          });

          imageUrl = await uploadImage(data.image);

          toast.success("Imagem enviada com sucesso!", {
            description: "Agora enviando as mensagens...",
          });
        } catch (uploadError) {
          console.error("Erro no upload da imagem:", uploadError);
          toast.error("Falha no upload da imagem", {
            description:
              uploadError instanceof Error
                ? uploadError.message
                : "Tente novamente.",
          });
          return;
        }
      }

      // Enviar mensagem via API (sempre usar mensagem original por enquanto)
      const response = await sendBroadcastMessage(
        validPhones,
        data.message,
        imageUrl,
        data.apiKey,
        data.instanceName
      );

      // Feedback de sucesso
      if (response.success) {
        const sentCount = response.data?.sentCount || validPhones.length;
        const failedCount = response.data?.failedCount || 0;

        toast.success(`Mensagem enviada com sucesso!`, {
          description: `${sentCount} mensagem${
            sentCount > 1 ? "s" : ""
          } enviada${sentCount > 1 ? "s" : ""}${
            failedCount > 0 ? `, ${failedCount} falharam` : ""
          }.`,
        });

        // Reset form apenas se tudo foi enviado com sucesso
        if (failedCount === 0) {
          setPhones([]);
          setMessage("");
          setImage(null);
          setImagePreview(null);
          setTitle("");
          setUseAiEnhancement(false);
          // Manter API Key e Instance Name para reutilização
          reset();
        }
      } else {
        throw new Error(response.message || "Falha no envio das mensagens");
      }
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);

      let errorMessage = "Erro ao enviar mensagem";
      let errorDescription = "Tente novamente em alguns instantes.";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        typeof error === "object" &&
        error !== null &&
        "message" in error
      ) {
        const apiError = error as ApiError;
        errorMessage = apiError.message;
        if (apiError.status === 401) {
          errorDescription = "Verifique as credenciais da API.";
        } else if (apiError.status === 429) {
          errorDescription = "Muitas requisições. Aguarde alguns minutos.";
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid =
    phones.length > 0 &&
    message.trim().length > 0 &&
    title.trim().length > 0 &&
    apiKey.trim().length > 0 &&
    instanceName.trim().length > 0;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-launcher-primary">
          Lancador de Mensagens
        </h1>
        <p className="text-launcher-secondary">
          Envie mensagens e imagens para múltiplos números de telefone
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Left Column - Form Inputs */}
          <div className="space-y-6">
            {/* Title */}
            <div>
              <Label
                htmlFor="title"
                className="text-launcher-primary font-medium"
              >
                Título da Mensagem *
              </Label>
              <Input
                id="title"
                type="text"
                placeholder="Digite o título/assunto da mensagem..."
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="mt-2 border-launcher-secondary/30 focus:border-launcher-accent"
                maxLength={100}
              />
              <div className="flex justify-between mt-1">
                {errors.title && (
                  <p className="text-red-500 text-sm font-medium">
                    {errors.title.message}
                  </p>
                )}
                <p className="text-sm text-gray-600 ml-auto">
                  {title.length}/100
                </p>
              </div>
            </div>

            {/* Phone Numbers */}
            <PhoneInputList
              phones={phones}
              onChange={setPhones}
              error={errors.phones?.message}
            />

            {/* Message */}
            <div>
              <Label
                htmlFor="message"
                className="text-launcher-primary font-medium"
              >
                Mensagem *
              </Label>
              <Textarea
                id="message"
                placeholder="Digite sua mensagem aqui..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="mt-2 min-h-[120px] border-launcher-secondary/30 focus:border-launcher-accent resize-none"
                maxLength={1000}
              />
              <div className="flex justify-between mt-1">
                {errors.message && (
                  <p className="text-red-500 text-sm font-medium">
                    {errors.message.message}
                  </p>
                )}
                <p className="text-sm text-gray-600 ml-auto">
                  {message.length}/1000
                </p>
              </div>
            </div>

            {/* AI Enhancement Switch */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
              <div className="flex-1">
                <Label
                  htmlFor="ai-enhancement"
                  className="text-launcher-primary font-medium cursor-pointer"
                >
                  Melhorar mensagem com IA
                </Label>
                <p className="text-sm text-gray-600 mt-1">
                  {useAiEnhancement
                    ? "A IA irá aprimorar sua mensagem antes do envio"
                    : "Enviar mensagem original sem modificações"}
                </p>
              </div>
              <Switch
                id="ai-enhancement"
                checked={useAiEnhancement}
                onCheckedChange={setUseAiEnhancement}
                className="ml-4"
              />
            </div>

            {/* Image Upload */}
            <ImageUploader
              image={image}
              onChange={setImage}
              error={errors.image?.message}
            />

            {/* API Key */}
            <div>
              <Label
                htmlFor="apiKey"
                className="text-launcher-primary font-medium"
              >
                API Key *
              </Label>
              <div className="relative mt-2">
                <Input
                  id="apiKey"
                  type={showApiKey ? "text" : "password"}
                  placeholder="Digite sua API Key..."
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  className="pr-10 border-launcher-secondary/30 focus:border-launcher-accent"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </Button>
              </div>
              {errors.apiKey && (
                <p className="text-red-500 text-sm font-medium mt-1">
                  {errors.apiKey.message}
                </p>
              )}
            </div>

            {/* Instance Name */}
            <div>
              <Label
                htmlFor="instanceName"
                className="text-launcher-primary font-medium"
              >
                Nome da Instância *
              </Label>
              <Input
                id="instanceName"
                type="text"
                placeholder="Digite o nome da instância..."
                value={instanceName}
                onChange={(e) => setInstanceName(e.target.value)}
                className="mt-2 border-launcher-secondary/30 focus:border-launcher-accent"
              />
              {errors.instanceName && (
                <p className="text-red-500 text-sm font-medium mt-1">
                  {errors.instanceName.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={!isFormValid || isSubmitting}
              className="w-full bg-launcher-secondary hover:bg-launcher-accent text-white font-medium py-3 text-base disabled:opacity-50"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Enviar Mensagem
                </>
              )}
            </Button>
          </div>

          {/* Right Column - Preview */}
          <div>
            <MessagePreview
              phones={phones}
              message={message}
              image={image}
              imagePreview={imagePreview}
              title={title}
            />
          </div>
        </div>
      </form>
    </div>
  );
};

export default MessageForm;
