import React, { useState, useEffect } from 'react';
import { Smile, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { loadEmojis, searchEmojis, EmojiCategory, EmojiData } from '@/lib/emojis';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
}

const EmojiPicker: React.FC<EmojiPickerProps> = ({ onEmojiSelect }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [categories, setCategories] = useState<EmojiCategory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<EmojiData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Carregar emojis quando o componente montar
  useEffect(() => {
    const loadEmojiData = async () => {
      setIsLoading(true);
      try {
        const emojiCategories = await loadEmojis();
        setCategories(emojiCategories);
      } catch (error) {
        console.error('Erro ao carregar emojis:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadEmojiData();
  }, []);

  // Buscar emojis quando a query mudar
  useEffect(() => {
    if (searchQuery.trim()) {
      const allEmojis = categories.flatMap(category => category.emojis);
      const results = searchEmojis(allEmojis, searchQuery);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, categories]);

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    setIsOpen(false);
    setSearchQuery('');
  };

  const renderEmojiGrid = (emojis: EmojiData[]) => (
    <div className="grid grid-cols-8 gap-2 max-h-64 overflow-y-auto p-2">
      {emojis.map((emojiData, index) => (
        <button
          key={`${emojiData.emoji}-${index}`}
          onClick={() => handleEmojiClick(emojiData.emoji)}
          className="p-2 text-2xl hover:bg-gray-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-launcher-accent"
          title={emojiData.tags.slice(0, 3).join(', ')}
        >
          {emojiData.emoji}
        </button>
      ))}
    </div>
  );

  return (
    <>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="absolute bottom-2 right-2 p-2 text-gray-500 hover:text-launcher-accent hover:bg-gray-100"
        title="Adicionar emoji"
      >
        <Smile className="h-4 w-4" />
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[600px]">
          <DialogHeader>
            <DialogTitle>Selecionar Emoji</DialogTitle>
          </DialogHeader>

          {/* Campo de busca */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar emojis..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500">Carregando emojis...</div>
            </div>
          ) : searchQuery.trim() ? (
            // Resultados da busca
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Resultados da busca ({searchResults.length})
              </h3>
              {searchResults.length > 0 ? (
                renderEmojiGrid(searchResults)
              ) : (
                <div className="text-center py-8 text-gray-500">
                  Nenhum emoji encontrado
                </div>
              )}
            </div>
          ) : (
            // Categorias de emojis
            <Tabs defaultValue={categories[0]?.name} className="w-full">
              <TabsList className="grid w-full grid-cols-5 mb-4">
                {categories.slice(0, 5).map((category) => (
                  <TabsTrigger
                    key={category.name}
                    value={category.name}
                    className="text-xs"
                    title={category.name}
                  >
                    <span className="text-lg">{category.icon}</span>
                  </TabsTrigger>
                ))}
              </TabsList>

              {categories.length > 5 && (
                <TabsList className="grid w-full grid-cols-5 mb-4">
                  {categories.slice(5, 10).map((category) => (
                    <TabsTrigger
                      key={category.name}
                      value={category.name}
                      className="text-xs"
                      title={category.name}
                    >
                      <span className="text-lg">{category.icon}</span>
                    </TabsTrigger>
                  ))}
                </TabsList>
              )}

              {categories.map((category) => (
                <TabsContent key={category.name} value={category.name}>
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">
                      {category.name} ({category.emojis.length})
                    </h3>
                    {renderEmojiGrid(category.emojis)}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EmojiPicker;
