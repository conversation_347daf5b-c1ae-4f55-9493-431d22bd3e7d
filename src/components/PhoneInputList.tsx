
import React, { useState } from 'react';
import { X, Plus, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface PhoneInputListProps {
  phones: string[];
  onChange: (phones: string[]) => void;
  error?: string;
}

const PhoneInputList: React.FC<PhoneInputListProps> = ({ phones, onChange, error }) => {
  const [newPhone, setNewPhone] = useState('');

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/\s+/g, ''));
  };

  const addPhone = () => {
    const cleanPhone = newPhone.trim();
    if (cleanPhone && validatePhone(cleanPhone) && !phones.includes(cleanPhone)) {
      onChange([...phones, cleanPhone]);
      setNewPhone('');
    }
  };

  const removePhone = (index: number) => {
    const updatedPhones = phones.filter((_, i) => i !== index);
    onChange(updatedPhones);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addPhone();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    const phoneNumbers = pastedText
      .split(/[\n,;]/)
      .map(phone => phone.trim())
      .filter(phone => phone && validatePhone(phone) && !phones.includes(phone));
    
    if (phoneNumbers.length > 0) {
      onChange([...phones, ...phoneNumbers]);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="phone-input" className="text-launcher-primary font-medium">
          Números de Telefone *
        </Label>
        <div className="flex gap-2 mt-2">
          <div className="relative flex-1">
            <Phone className="absolute left-3 top-3 h-4 w-4 text-launcher-secondary" />
            <Input
              id="phone-input"
              type="tel"
              placeholder="+55 11 99999-9999"
              value={newPhone}
              onChange={(e) => setNewPhone(e.target.value)}
              onKeyPress={handleKeyPress}
              onPaste={handlePaste}
              className="pl-10 border-launcher-secondary/30 focus:border-launcher-accent"
            />
          </div>
          <Button
            type="button"
            onClick={addPhone}
            disabled={!newPhone.trim() || !validatePhone(newPhone.trim())}
            className="bg-launcher-secondary hover:bg-launcher-accent text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Cole múltiplos números separados por vírgula ou quebra de linha
        </p>
      </div>

      {phones.length > 0 && (
        <div className="space-y-2">
          <Label className="text-launcher-primary font-medium">
            Números Adicionados ({phones.length})
          </Label>
          <div className="max-h-32 overflow-y-auto space-y-1 p-3 bg-gray-50 rounded-md border">
            {phones.map((phone, index) => (
              <div
                key={index}
                className="flex items-center justify-between bg-white p-2 rounded border"
              >
                <span className="text-launcher-primary font-mono text-sm">{phone}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removePhone(index)}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {error && (
        <p className="text-red-500 text-sm font-medium">{error}</p>
      )}
    </div>
  );
};

export default PhoneInputList;
